#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
功能：验证三个问题的修复情况

修复内容：
1. 休息触发条件和休息时长设置界面问题
2. 鼠标移动功能的视觉反馈
3. 移除基础参数模块

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import logging
import time
import threading

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_mouse_visual_feedback():
    """测试鼠标视觉反馈功能"""
    try:
        from modules.mouse_visual_feedback import get_mouse_feedback_instance
        
        print("🖱️ 测试鼠标视觉反馈功能...")
        
        # 创建视觉反馈实例
        config = {
            "trail_enabled": True,
            "trail_color": "#FF0000",
            "trail_width": 3,
            "trail_duration": 2.0,
            "click_highlight_enabled": True,
            "click_highlight_color": "#00FF00",
            "click_highlight_size": 20,
            "click_highlight_duration": 1.5,
            "animation_enabled": True,
            "animation_steps": 15,
            "show_coordinates": True
        }
        
        mouse_feedback = get_mouse_feedback_instance(config)
        
        # 测试鼠标移动
        import pyautogui
        current_pos = pyautogui.position()
        start_pos = (current_pos.x, current_pos.y)
        end_pos = (current_pos.x + 100, current_pos.y + 100)
        
        print(f"   移动鼠标从 {start_pos} 到 {end_pos}")
        success = mouse_feedback.enhanced_move_to(start_pos, end_pos, 1.0, "测试移动")
        
        if success:
            print("   ✅ 鼠标视觉反馈测试成功")
            
            # 测试点击效果
            mouse_feedback.show_click_effect(end_pos, "测试点击")
            print("   ✅ 点击效果测试成功")
            
            # 清理
            time.sleep(3)
            mouse_feedback.cleanup_all()
            print("   ✅ 清理完成")
            
            return True
        else:
            print("   ❌ 鼠标视觉反馈测试失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 鼠标视觉反馈测试异常: {e}")
        return False

def test_gui_interface():
    """测试GUI界面修复"""
    try:
        print("🖥️ 测试GUI界面修复...")
        
        # 导入GUI模块
        from wechat_automation_gui import WeChatAutomationGUI

        # 创建GUI实例（它会自己创建root窗口）
        gui = WeChatAutomationGUI()
        gui.root.withdraw()  # 隐藏主窗口
        
        # 检查是否存在基础参数相关的属性（应该已被移除）
        has_batch_size_var = hasattr(gui, 'batch_size_var')
        has_delay_var = hasattr(gui, 'delay_var')
        
        if has_batch_size_var or has_delay_var:
            print("   ❌ 基础参数模块未完全移除")
            print(f"      batch_size_var存在: {has_batch_size_var}")
            print(f"      delay_var存在: {has_delay_var}")
            gui.root.destroy()
            return False
        else:
            print("   ✅ 基础参数模块已成功移除")

        # 检查休息设置控件是否存在
        has_rest_trigger_entry = hasattr(gui, 'rest_trigger_entry')
        has_rest_duration_entry = hasattr(gui, 'rest_duration_entry')

        if has_rest_trigger_entry and has_rest_duration_entry:
            print("   ✅ 休息设置控件存在且正确")
        else:
            print("   ❌ 休息设置控件缺失")
            print(f"      rest_trigger_entry存在: {has_rest_trigger_entry}")
            print(f"      rest_duration_entry存在: {has_rest_duration_entry}")
            gui.root.destroy()
            return False

        # 检查运行时参数是否正确初始化
        has_runtime_params = hasattr(gui, 'runtime_params')
        if has_runtime_params:
            rest_trigger_exists = 'rest_trigger' in gui.runtime_params
            rest_duration_exists = 'rest_duration' in gui.runtime_params

            if rest_trigger_exists and rest_duration_exists:
                print("   ✅ 运行时参数正确初始化")
            else:
                print("   ❌ 运行时参数初始化不完整")
                print(f"      rest_trigger存在: {rest_trigger_exists}")
                print(f"      rest_duration存在: {rest_duration_exists}")
                gui.root.destroy()
                return False
        else:
            print("   ❌ 运行时参数未初始化")
            gui.root.destroy()
            return False

        gui.root.destroy()
        print("   ✅ GUI界面修复测试成功")
        return True
        
    except Exception as e:
        print(f"   ❌ GUI界面测试异常: {e}")
        import traceback
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_main_interface_integration():
    """测试主界面模块的视觉反馈集成"""
    try:
        print("🔧 测试主界面模块视觉反馈集成...")
        
        from modules.main_interface import WeChatMainInterface
        
        # 创建主界面实例
        interface = WeChatMainInterface()
        
        # 检查是否有鼠标反馈实例
        has_mouse_feedback = hasattr(interface, 'mouse_feedback')
        
        if has_mouse_feedback and interface.mouse_feedback is not None:
            print("   ✅ 主界面模块已集成鼠标视觉反馈")
            
            # 测试_safe_click方法是否存在
            has_safe_click = hasattr(interface, '_safe_click')
            if has_safe_click:
                print("   ✅ _safe_click方法存在")
            else:
                print("   ❌ _safe_click方法不存在")
                return False
                
            return True
        else:
            print("   ❌ 主界面模块未正确集成鼠标视觉反馈")
            return False
            
    except Exception as e:
        print(f"   ❌ 主界面模块测试异常: {e}")
        import traceback
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def test_frequency_error_handler_integration():
    """测试频率错误处理器的视觉反馈集成"""
    try:
        print("⚠️ 测试频率错误处理器视觉反馈集成...")
        
        from modules.frequency_error_handler import FrequencyErrorHandler
        
        # 创建错误处理器实例
        handler = FrequencyErrorHandler()
        
        # 检查是否有鼠标反馈实例
        has_mouse_feedback = hasattr(handler, 'mouse_feedback')
        
        if has_mouse_feedback:
            print("   ✅ 频率错误处理器已集成鼠标视觉反馈")
            return True
        else:
            print("   ❌ 频率错误处理器未正确集成鼠标视觉反馈")
            return False
            
    except Exception as e:
        print(f"   ❌ 频率错误处理器测试异常: {e}")
        import traceback
        print(f"   详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 微信自动化GUI修复测试")
    print("=" * 60)
    print()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    test_results = []
    
    # 测试1：鼠标视觉反馈功能
    print("📋 测试1：鼠标视觉反馈功能")
    result1 = test_mouse_visual_feedback()
    test_results.append(("鼠标视觉反馈", result1))
    print()
    
    # 测试2：GUI界面修复
    print("📋 测试2：GUI界面修复")
    result2 = test_gui_interface()
    test_results.append(("GUI界面修复", result2))
    print()
    
    # 测试3：主界面模块集成
    print("📋 测试3：主界面模块视觉反馈集成")
    result3 = test_main_interface_integration()
    test_results.append(("主界面模块集成", result3))
    print()
    
    # 测试4：频率错误处理器集成
    print("📋 测试4：频率错误处理器视觉反馈集成")
    result4 = test_frequency_error_handler_integration()
    test_results.append(("频率错误处理器集成", result4))
    print()
    
    # 汇总结果
    print("=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            success_count += 1
    
    print()
    print(f"总计: {success_count}/{len(test_results)} 项测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有测试通过！修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        input("\n按回车键退出...")
        sys.exit(1)
